const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

contextBridge.exposeInMainWorld('electronAPI', {
  // Existing handlers
  install: (data) => ipcRenderer.invoke('install', data),
  configureIIS: (data) => ipcRenderer.invoke('configureIIS', data),
  
  // New handlers for update flow
  browseDirectory: () => ipcRenderer.invoke('browse-directory'),
  validateInstallation: (data) => ipcRenderer.invoke('validate-installation', data),
  updateInstallation: (data) => ipcRenderer.invoke('update-installation', data)
});
