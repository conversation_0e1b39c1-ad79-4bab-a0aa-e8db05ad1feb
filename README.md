# Instalador de Intelibots

Aplicación de escritorio desarrollada con Electron para instalar y configurar el servicio de Intelibots de manera sencilla y segura.

## Características

- Interfaz gráfica intuitiva y moderna
- Instalación automatizada
- Configuración del archivo .env
- Manejo de errores y retroalimentación visual

## Requisitos Previos

- Node.js (versión 14 o superior)
- npm o yarn

## Instalación para Desarrollo

1. Clonar el repositorio:
```bash
git clone [url-del-repositorio]
cd intelibots-installer
```

2. Instalar dependencias:
```bash
npm install
```

3. Iniciar la aplicación en modo desarrollo:
```bash
npm start
```

## Estructura del Proyecto

```
├── assets/           # Recursos estáticos
│   └── Archive.zip   # Archivo ZIP con el código fuente de la API
├── index.html       # Interfaz de usuario
├── main.js          # Proceso principal de Electron
├── preload.js       # Script de precarga
└── package.json     # Configuración del proyecto
```

El archivo `Archive.zip` debe contener el código fuente de la API que será instalada. Este archivo debe estar ubicado en la carpeta `assets` y debe llamarse exactamente `Archive.zip` para que el instalador pueda encontrarlo y extraerlo correctamente.

## Scripts Disponibles

- `npm start`: Inicia la aplicación en modo desarrollo
- `npm run pack`: Genera un directorio con la aplicación empaquetada
- `npm run dist`: Crea el instalador distribuible

## Construcción

Para generar el instalador distribuible:

```bash
npm run dist
```

Esto generará los archivos de instalación en el directorio `dist/`.

## Tecnologías Utilizadas

- Electron
- HTML/CSS/JavaScript
- adm-zip (para manejo de archivos ZIP)
- dotenv (para configuración de variables de entorno)

## Licencia

ISC