# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# Entorno de ejecución
node_modules/
dist/
out/
release-builds/

# Archivos de entorno
.env
.env.local
.env.*.local
.env.development.local
.env.test.local
.env.production.local

# Cache de dependencias
.npm
.yarn/
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*
.pnpm-store/

# Testing y cobertura
coverage/
.nyc_output/

# Electrón
*.exe
*.dmg
*.AppImage
*.snap
*.deb
*.rpm
app/build/
app/dist/
app/node_modules/
!app/package.json

# Archivos de sistemas operativos
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDEs y editores
.vscode/
.idea/
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Paquetes compilados
*.7z
*.dmg
*.gz
*.iso
*.jar
*.rar
*.tar
*.zip

# Archivos de lock (comenta si usas package-lock.json)
# package-lock.json
# yarn.lock
# pnpm-lock.yaml

# Otros
tmp/
temp/
*.tmp
*.bak
*.patch