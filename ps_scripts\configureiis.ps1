param(
    [Parameter(Mandatory = $true)]
    [string]$siteName,

    [Parameter(Mandatory = $true)]
    [int]$port
)

# Validar que el script se ejecute en modo Administrador
$currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
$windowsPrincipal = New-Object Security.Principal.WindowsPrincipal($currentUser)
if (-not $windowsPrincipal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator))
{
    Write-Host "Este script debe ejecutarse con privilegios de Administrador." -ForegroundColor Red
    exit 1
}

# Verificar si la clave del registro de Python 3.11 existe
$pythonKeyPath = "HKLM:\Software\Python\PythonCore\3.11\InstallPath"

try {
    $pythonProperties = Get-ItemProperty -Path $pythonKeyPath -ErrorAction Stop
} catch {
    Write-Host "No se encontró Python 3.11 en el registro. Asegúrate de que esté instalado." -ForegroundColor Red
    exit 1
}

# Se asume que la ruta predeterminada está en el valor (Default)
$pythonInstallationPath = $pythonProperties.'(default)'

# Eliminar comillas extras en caso de existir
$pythonInstallationPath = $pythonInstallationPath.Trim('"')

if ([string]::IsNullOrWhiteSpace($pythonInstallationPath)) {
    Write-Host "No se encontró la ruta de instalación en el registro." -ForegroundColor Red
    exit 1
}

Write-Host "Ruta de instalación de Python 3.11: '$pythonInstallationPath'" -ForegroundColor Green

# Asegurarse que la ruta termina con una barra invertida (si no, agregamos una para efectos de visualización)
if ($pythonInstallationPath[-1] -ne '\') {
    $pythonInstallationPath += "\"
}

# Antes de envolver la ruta en comillas, eliminar la barra invertida final para evitar problemas
if ($pythonInstallationPath[-1] -eq '\') {
    $pythonInstallationPath = $pythonInstallationPath.Substring(0, $pythonInstallationPath.Length - 1)
}

# Envolver la ruta en comillas para usarla con icacls
$quotedPythonPath = '"{0}"' -f $pythonInstallationPath
Write-Host "Usando ruta para icacls: $quotedPythonPath"

$aclResult = icacls $quotedPythonPath /grant "IIS_IUSRS:(OI)(CI)F" /T

if ($LASTEXITCODE -eq 0) {
    Write-Host "Permisos configurados correctamente para IIS_IUSRS en $pythonInstallationPath" -ForegroundColor Green
} else {
    Write-Host "Se produjo un error al configurar permisos." -ForegroundColor Red
    Write-Host $aclResult
}

# Obtener la carpeta del sitio (se asume que es el directorio actual)
$folderPath = Get-Location

# Otorgar permisos al grupo IIS_IUSRS sobre la carpeta del sitio
Write-Output "Otorgando permisos a IIS_IUSRS sobre $folderPath ..."
icacls $folderPath /grant "IIS_IUSRS:(OI)(CI)F" | Out-Null

# Importar el módulo WebAdministration para gestionar IIS
Import-Module WebAdministration

# Crear el sitio en IIS si no existe
if (Test-Path "IIS:\Sites\$siteName") {
    Write-Output "El sitio '$siteName' ya existe."
} else {
    # Crear un nuevo sitio en IIS usando el puerto especificado
    New-Item "IIS:\Sites\$siteName" -bindings @{protocol="http";bindingInformation="*:${port}:"} `
      -physicalPath $folderPath
    Write-Output "Sitio '$siteName' creado en el puerto $port con la carpeta $folderPath."
}
