{"name": "intelibots-installer", "version": "1.0.2", "main": "main.js", "scripts": {"start": "electron .", "pack": "electron-builder --dir", "dist": "electron-builder"}, "devDependencies": {"electron": "^28.1.0", "electron-builder": "^24.9.1"}, "dependencies": {"adm-zip": "^0.5.10", "child-process": "^1.0.2", "dotenv": "^16.3.1", "electron-util": "^0.17.2", "sudo-prompt": "^9.2.1"}, "build": {"appId": "com.intelisis.intelibots-installer", "win": {"target": "portable", "icon": "icon.ico"}}, "keywords": [], "author": "", "license": "ISC", "description": ""}