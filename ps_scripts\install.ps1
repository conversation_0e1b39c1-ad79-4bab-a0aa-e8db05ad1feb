# Opcional: Verificar que se está ejecutando como administrador
$currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
$windowsPrincipal = New-Object Security.Principal.WindowsPrincipal($currentUser)
if (-not $windowsPrincipal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)) {
    Write-Host "Este script debe ejecutarse con privilegios de Administrador." -ForegroundColor Red
    exit 1
}

# Mostrar la ruta donde reside el script para depuración
Write-Host "PSScriptRoot: $PSScriptRoot"

# 1. Crear el entorno virtual (.venv) en la carpeta del proyecto
Write-Host "Creando entorno virtual en: $PSScriptRoot\.venv" -ForegroundColor Green
python -m venv "$PSScriptRoot\.venv"

# 2. Construir la ruta al ejecutable pip dentro del entorno virtual
$pipPath = Join-Path $PSScriptRoot ".venv\Scripts\pip.exe"
if (-not (Test-Path $pipPath)) {
    Write-Host "No se encontró pip en: $pipPath" -ForegroundColor Red
    exit 1
}

# Instalar las dependencias listadas en el archivo requirements.txt
$requirementsPath = Join-Path $PSScriptRoot "requirements.txt"
if (-not (Test-Path $requirementsPath)) {
    Write-Host "No se encontró requirements.txt en: $PSScriptRoot" -ForegroundColor Red
    exit 1
}
Write-Host "Instalando dependencias desde: $requirementsPath" -ForegroundColor Green
& $pipPath install -r $requirementsPath

if ($LASTEXITCODE -ne 0) {
    Write-Host "Error durante la instalación de dependencias." -ForegroundColor Red
    exit 1
}

# 3. Otorgar permisos completos al grupo IIS_IUSRS sobre todo el directorio del proyecto
Write-Host "Otorgando permisos completos a IIS_IUSRS en: $PSScriptRoot" -ForegroundColor Green
$aclResult = icacls "$PSScriptRoot" /grant "IIS_IUSRS:(OI)(CI)F" /T

if ($LASTEXITCODE -eq 0) {
    Write-Host "Permisos configurados correctamente para IIS_IUSRS." -ForegroundColor Green
} else {
    Write-Host "Se produjo un error al configurar permisos." -ForegroundColor Red
    Write-Host $aclResult
}