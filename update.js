(function () {
  console.log('✔ update.js loaded and executing');

  const apiPathInput = document.getElementById('apiPath');
  const browseBtn = document.getElementById('browseBtn');
  const iisSiteNameInput = document.getElementById('iisSiteName');
  const validateBtn = document.getElementById('validateBtn');
  const updateBtn = document.getElementById('updateBtn');
  const updateConfigDiv = document.getElementById('updateConfig');
  const updateStatusDiv = document.getElementById('updateStatus');
  const progressBar = document.getElementById('progressBar');
  const progressStatus = document.getElementById('progressStatus');

  if (!browseBtn || !validateBtn || !updateBtn) {
    console.error('Elementos necesarios no encontrados en el DOM');
    return;
  }

  let currentEnv = {};

  browseBtn.addEventListener('click', async () => {
    try {
      const path = await window.electronAPI.browseDirectory();
      console.log('Selected path:', path);
      if (path) apiPathInput.value = path;
    } catch (error) {
      showError('Error al seleccionar directorio: ' + error.message);
    }
  });

  validateBtn.addEventListener('click', async () => {
    const path = apiPathInput.value.trim();
    const siteName = iisSiteNameInput.value.trim();
    if (!path) {
      showError('Por favor, selecciona la ruta de la API');
      return;
    }
    if (!siteName) {
      showError('Por favor, ingresa el nombre del sitio IIS');
      return;
    }
    validateBtn.disabled = true;
    validateBtn.textContent = 'Validando...';
    updateStatusDiv.textContent = 'Validando instalación...';
    updateStatusDiv.className = 'installing';

    try {
      const result = await window.electronAPI.validateInstallation({
        path,
        siteName,
      });
      if (result.valid) {
        currentEnv = result.env;
        populateEnvForm(result.env);
        updateConfigDiv.style.display = 'block';
        updateStatusDiv.className = 'success';
        updateStatusDiv.textContent =
          'Instalación validada correctamente';
      } else {
        showError(result.error || 'Error al validar la instalación');
      }
    } catch (error) {
      showError('Error al validar la instalación: ' + error.message);
    } finally {
      validateBtn.disabled = false;
      validateBtn.textContent = 'Validar';
    }
  });

  updateBtn.addEventListener('click', async () => {
    const path = apiPathInput.value.trim();
    const siteName = iisSiteNameInput.value.trim();
    if (!path || !siteName) {
      showError('Por favor, completa todos los campos requeridos');
      return;
    }
    // Only allow updates to specific fields
    const updatedEnv = {
      ...currentEnv,
      // OpenAI fields - use current values (no updates allowed)
      OPENAI_API_KEY: currentEnv.OPENAI_API_KEY,
      OPENAI_ORG: currentEnv.OPENAI_ORG,
      // Pinecone fields - use current values (no updates allowed)
      PINECONE_API_KEY: currentEnv.PINECONE_API_KEY,
      // Gemini field - use current values (no updates allowed)
      GEMINI_API_KEY: currentEnv.GEMINI_API_KEY,
      // SQL fields - only allow database updates, keep others as current values
      SQL_USER: currentEnv.SQL_USER,
      SQL_PASSWORD: currentEnv.SQL_PASSWORD,
      SQL_SERVER: currentEnv.SQL_SERVER,
      SQL_PORT: currentEnv.SQL_PORT,
      // Only SQL_DATABASE can be updated
      SQL_DATABASE: document.getElementById('updateSqlDatabase').value || currentEnv.SQL_DATABASE,
    };

    showScreen('progressScreen');
    updateProgress(10, 'Iniciando actualización...');

    try {
      updateProgress(30, 'Actualizando archivos...');
      const result = await window.electronAPI.updateInstallation({
        path,
        env: updatedEnv,
        siteName,
      });

      if (result.success) {
        updateProgress(100, 'Actualización completada con éxito');
        setTimeout(() => showScreen('completionScreen'), 1000);
      } else {
        throw new Error(
          result.error || 'Error desconocido durante la actualización'
        );
      }
    } catch (error) {
      showScreen('updateScreen');
      showError('Error durante la actualización: ' + error.message);
    }
  });

  function populateEnvForm(env) {
    // OpenAI fields - readonly (no updates allowed)
    const openaiKeyField = document.getElementById('updateOpenaiKey');
    const openaiOrgField = document.getElementById('updateOpenaiOrg');
    openaiKeyField.value = env.OPENAI_API_KEY || '';
    openaiOrgField.value = env.OPENAI_ORG || '';
    openaiKeyField.readOnly = true;
    openaiOrgField.readOnly = true;
    openaiKeyField.style.backgroundColor = '#f5f5f5';
    openaiOrgField.style.backgroundColor = '#f5f5f5';

    // Pinecone fields - readonly (no updates allowed)
    const pineconeKeyField = document.getElementById('updatePineconeKey');
    pineconeKeyField.value = env.PINECONE_API_KEY || '';
    pineconeKeyField.readOnly = true;
    pineconeKeyField.style.backgroundColor = '#f5f5f5';

    // Gemini field - readonly (no updates allowed)
    const geminiKeyField = document.getElementById('updateGeminiKey');
    geminiKeyField.value = env.GEMINI_API_KEY || '';
    geminiKeyField.readOnly = true;
    geminiKeyField.style.backgroundColor = '#f5f5f5';

    // SQL fields - most readonly except database
    const sqlUserField = document.getElementById('updateSqlUser');
    const sqlPasswordField = document.getElementById('updateSqlPassword');
    const sqlServerField = document.getElementById('updateSqlServer');
    const sqlPortField = document.getElementById('updateSqlPort');
    const sqlDatabaseField = document.getElementById('updateSqlDatabase');

    sqlUserField.value = env.SQL_USER || '';
    sqlPasswordField.value = env.SQL_PASSWORD || '';
    sqlServerField.value = env.SQL_SERVER || '';
    sqlPortField.value = env.SQL_PORT || '';
    sqlDatabaseField.value = env.SQL_DATABASE || '';

    // Make SQL fields readonly except database
    sqlUserField.readOnly = true;
    sqlPasswordField.readOnly = true;
    sqlServerField.readOnly = true;
    sqlPortField.readOnly = true;

    // Style readonly SQL fields
    sqlUserField.style.backgroundColor = '#f5f5f5';
    sqlPasswordField.style.backgroundColor = '#f5f5f5';
    sqlServerField.style.backgroundColor = '#f5f5f5';
    sqlPortField.style.backgroundColor = '#f5f5f5';

    // Database field remains editable (normal styling)
    sqlDatabaseField.readOnly = false;
    sqlDatabaseField.style.backgroundColor = '';
  }

  function showError(message) {
    console.error('Error:', message);
    updateStatusDiv.className = 'error';
    updateStatusDiv.textContent = message;
    updateStatusDiv.style.display = 'block';
  }

  function updateProgress(percent, message) {
    progressBar.style.width = `${percent}%`;
    progressStatus.textContent = message;
  }
})();