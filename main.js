const { app, BrowserWindow, ipcMain, dialog } = require('electron');
const path = require('path');
const fs = require('fs');
const { exec } = require('child_process');
const AdmZip = require('adm-zip');
const sudo = require('sudo-prompt');
const dotenv = require('dotenv');

let mainWindow;

function createWindow() {
  mainWindow = new BrowserWindow({
    width: 1280,
    height: 720,
    webPreferences: {
      preload: path.join(__dirname, 'preload.js'),
      nodeIntegration: false,
      contextIsolation: true
    }
  });

  // Abrir DevTools automáticamente para depuración
  mainWindow.webContents.openDevTools();

  // Agregar manejo de errores al cargar el archivo
  mainWindow.loadFile('index.html').catch(err => {
    console.error('Error al cargar index.html:', err);
  });
  
  // Escuchar eventos de error en la carga de la página
  mainWindow.webContents.on('did-fail-load', (event, errorCode, errorDescription) => {
    console.error(`Error al cargar la página: ${errorDescription} (${errorCode})`);
  });
}

app.whenReady().then(createWindow);

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});

// Handler for browsing directories
ipcMain.handle('browse-directory', async () => {
  console.log('Received browse-directory IPC invoke');
  const result = await dialog.showOpenDialog({
    properties: ['openDirectory']
  });
  console.log('showOpenDialog result:', result);
  
  if (!result.canceled && result.filePaths.length > 0) {
    return result.filePaths[0];
  }
  return null;
});

// Handler for validating installation directory
ipcMain.handle('validate-installation', async (event, { path: installPath, siteName }) => {
  try {
    // Check if .env file exists
    const envPath = path.join(installPath, '.env');
    if (!fs.existsSync(envPath)) {
      return { valid: false, error: 'No se encontró el archivo .env en la ruta especificada' };
    }

    // Check if site exists in IIS (usando sudo-prompt)
    const checkSiteCommand = `powershell -Command "Import-Module WebAdministration; Get-Website -Name '${siteName}'"`;
    const siteExists = await new Promise((resolve) => {
      sudo.exec(checkSiteCommand, { name: 'Intelibots' }, (error, stdout, stderr) => {
        console.log('PowerShell stdout:', stdout);
        console.log('PowerShell stderr:', stderr);
        resolve(stdout && stdout.trim().length > 0);
      });
    });

    if (!siteExists) {
      return { valid: false, error: `No se encontró el sitio IIS '${siteName}'` };
    }

    // Read and parse .env file
    const envContent = fs.readFileSync(envPath, 'utf8');
    const envConfig = dotenv.parse(envContent);

    return {
      valid: true,
      env: envConfig
    };
  } catch (error) {
    console.error('Error validating installation:', error);
    return { valid: false, error: `Error al validar la instalación: ${error.message}` };
  }
});

// Handler for updating installation
ipcMain.handle('update-installation', async (event, { path: installPath, env, siteName }) => {
  try {
    // 1. Crear carpeta de backup
    const backupDir = path.join(
      installPath,
      'backup_' + new Date().toISOString().replace(/[:.]/g, '-')
    );
    if (!fs.existsSync(backupDir)) {
      fs.mkdirSync(backupDir, { recursive: true });
    }

    // 2. Hacer backup de todos los archivos y carpetas, excepto .env, .venv y cualquier carpeta backup_*
    const files = fs.readdirSync(installPath);
    for (const file of files) {
      if (
        file === '.env' ||
        file === '.venv' ||
        file.startsWith('backup_')
      ) {
        continue;
      }
      const srcPath = path.join(installPath, file);
      const destPath = path.join(backupDir, file);

      if (fs.lstatSync(srcPath).isDirectory()) {
        await fs.promises.cp(srcPath, destPath, { recursive: true });
      } else {
        await fs.promises.copyFile(srcPath, destPath);
      }
    }

    // 3. Extraer nuevos archivos del ZIP (sobrescribe los existentes)
    const zipPath = path.join(__dirname, 'assets', 'Archive.zip');
    if (!fs.existsSync(zipPath)) {
      throw new Error('No se encontró el archivo de actualización (Archive.zip)');
    }

    const zip = new AdmZip(zipPath);
    zip.extractAllTo(installPath, true);

    // 4. Actualizar .env con los nuevos valores
    const envPath = path.join(installPath, '.env');
    let envContent = '';
    for (const [key, value] of Object.entries(env)) {
      if (value !== undefined && value !== '') {
        envContent += `${key}=${value}\n`;
      }
    }
    fs.writeFileSync(envPath, envContent);

    // 5. Reiniciar el sitio IIS (App Pool) con permisos elevados
    const restartCommand = `powershell -Command "Import-Module WebAdministration; Restart-WebAppPool -Name '${siteName}'"`;
    await new Promise((resolve, reject) => {
      sudo.exec(restartCommand, { name: 'Intelibots' }, (error, stdout, stderr) => {
        if (error) {
          console.error('Error reiniciando AppPool:', stderr || error);
          return reject(error);
        }
        resolve();
      });
    });

    return { success: true };
  } catch (error) {
    console.error('Error updating installation:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('install', async (event, data) => {
  try {
    const destination = data.destination;
    const zipPath = path.join(__dirname, 'assets', 'Archive.zip');
    const envPath = path.join(destination, '.env');

    // Asegúrate de que el directorio de destino exista
    if (!fs.existsSync(destination)) {
      fs.mkdirSync(destination, { recursive: true });
    }

    // 1. Extraer ZIP
    const AdmZip = require('adm-zip');
    const zip = new AdmZip(zipPath);
    zip.extractAllTo(destination, true);

    // 2. Crear o modificar .env (variables separadas)
    const envContent = `OPENAI_API_KEY=${data.openaiKey || ''}
OPENAI_ORG=${data.openaiOrg || ''}
GEMINI_API_KEY=${data.geminiKey || ''}
PINECONE_API_KEY=${data.pineconeKey}
PINECONE_INDEX_NAME=${data.pineconeIndexName}
SQL_USER=${data.sqlUser}
SQL_PASSWORD=${data.sqlPassword || ''}
SQL_SERVER=${data.sqlServer}
SQL_PORT=${data.sqlPort}
SQL_DATABASE=${data.sqlDatabase}`;
    fs.writeFileSync(envPath, envContent);

    // 3. Ejecutar el script de PowerShell install.ps1 en el directorio destino
    const scriptPath = path.join(destination, 'install.ps1');
    const command = `powershell.exe -ExecutionPolicy ByPass -File "${scriptPath}"`;

    const options = {
      name: 'Intelibots Installer'
    };    

    const result = await new Promise((resolve, reject) => {
      sudo.exec(command, options, (error, stdout, stderr) => {
        if (error) {
          console.error('Error ejecutando install.ps1:', error);
          return reject(error);
        }

        // Procesamos stderr: se ignoran líneas que comiencen con "[notice]"
        const lines = stderr.split(/\r?\n/).map(line => line.trim());
        const filteredLines = lines.filter(
          (line) => line && !line.startsWith('[notice]')
        );

        if (filteredLines.length > 0) {
          console.error('stderr:', filteredLines.join('\n'));
          return reject(new Error(filteredLines.join('\n')));
        }

        console.log('stdout:', stdout);
        resolve({ stdout });
      });
    });

    // 4. Configurar IIS automáticamente si se proporcionaron siteName y sitePort
    if (data.siteName && data.sitePort) {
      const configScriptPath = path.join(destination, 'configureiis.ps1');
      const configCommand = `cd /d "${destination}" && powershell.exe -ExecutionPolicy ByPass -File "${configScriptPath}" -port "${data.sitePort}" -siteName "${data.siteName}"`;

      const configResult = await new Promise((resolve, reject) => {
        sudo.exec(configCommand, options, (error, stdout, stderr) => {
          if (error) {
            console.error('Error configurando IIS:', error);
            return reject(error);
          }

          // Procesamos stderr: se ignoran líneas que comiencen con "[notice]"
          const lines = stderr.split(/\r?\n/).map(line => line.trim());
          const filteredLines = lines.filter(
            (line) => line && !line.startsWith('[notice]')
          );

          if (filteredLines.length > 0) {
            console.error('stderr en configuración IIS:', filteredLines.join('\n'));
            return reject(new Error(filteredLines.join('\n')));
          }

          console.log('stdout configuración IIS:', stdout);
          resolve({ stdout });
        });
      });
    }

    return { success: true, result };
  } catch (error) {
    console.error('Error durante la instalación:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('configureIIS', async (event, data) => {
  try {
    const destination = data.destination;
    const scriptPath = path.join(destination, 'configureiis.ps1');
    const sitePort = data.sitePort;
    const siteName = data.siteName;

    // Cambiar al directorio destino primero para que el script se ejecute en el lugar correcto
    const command = `cd /d "${destination}" && powershell.exe -ExecutionPolicy ByPass -File "${scriptPath}" -port "${sitePort}" -siteName "${siteName}"`;
    const options = { name: 'Intelibots Installer' };

    const result = await new Promise((resolve, reject) => {
      sudo.exec(command, options, (error, stdout, stderr) => {
        if (error) {
          console.error('Error ejecutando configureiis.ps1:', error);
          return reject(error);
        }

        // Procesar stderr: se ignoran líneas que comiencen con "[notice]"
        const lines = stderr.split(/\r?\n/).map(line => line.trim());
        const filteredLines = lines.filter(
          (line) => line && !line.startsWith("[notice]")
        );

        if (filteredLines.length > 0) {
          console.error('stderr:', filteredLines.join('\n'));
          return reject(new Error(filteredLines.join('\n')));
        }

        console.log('stdout:', stdout);
        resolve({ stdout });
      });
    });

    return { success: true, result };
  } catch (error) {
    console.error('Error durante la configuración de IIS:', error);
    return { success: false, error: error.message };
  }
});