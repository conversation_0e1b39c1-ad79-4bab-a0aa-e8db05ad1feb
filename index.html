<!DOCTYPE html>
<html>
  <head>
    <meta charset="UTF-8" />
    <title>Instalador Intelibots</title>
    <style>
      body {
        font-family: 'Segoe UI', Arial, sans-serif;
        min-height: 100vh;
        margin: 0;
        padding: 20px;
        background: linear-gradient(135deg, #0078d4 0%, #004578 100%);
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .container {
        background: rgba(255, 255, 255, 0.95);
        padding: 30px;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        width: 90%;
        max-width: 500px;
      }
      .form-group {
        margin-bottom: 15px;
      }
      .form-group label {
        display: block;
        margin-bottom: 5px;
        color: #333;
        font-size: 14px;
      }
      .form-group input {
        width: 100%;
        padding: 8px;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 14px;
        box-sizing: border-box;
      }
      .form-group input:focus {
        border-color: #0078d4;
        outline: none;
        box-shadow: 0 0 0 2px rgba(0, 120, 212, 0.2);
      }
      h1 {
        color: #1a1a1a;
        margin: 0 0 25px 0;
        font-size: 28px;
        text-align: center;
      }
      /* General h2, can be overridden by more specific selectors */
      h2 {
        color: #1976d2; /* Default color for h2 */
        margin-top: 24px;
        margin-bottom: 12px;
        font-size: 20px;
        text-align: left;
      }
      button {
        background: linear-gradient(45deg, #0078d4, #2b88d8);
        color: white;
        border: none;
        padding: 12px 25px;
        border-radius: 8px;
        cursor: pointer;
        font-size: 16px;
        width: 100%;
        margin-top: 20px;
      }
      button:disabled {
        background: #cccccc;
        cursor: not-allowed;
      }
      .secondary-btn {
        background: #6c757d;
        margin-top: 10px;
      }
      .secondary-btn:hover {
        background: #5a6268;
      }
      input[type='radio'] {
        width: auto;
        margin-right: 10px;
      }
      #status {
        margin-top: 20px;
        padding: 15px;
        border-radius: 8px;
        font-size: 15px;
        text-align: center;
      }
      .success {
        background-color: #e6f4ea;
        color: #1e7e34;
      }
      .error {
        background-color: #fde7e9;
        color: #d32f2f;
      }
      .installing {
        background-color: #e3f2fd;
        color: #1976d2;
      }
      /* Update/Progress styles */
      #updateStatus {
        margin-top: 16px;
        padding: 8px;
        border-radius: 4px;
        display: none;
      }
      #updateStatus.success {
        background: #e0f7e9;
        color: #1b5e20;
        border: 1px solid #43a047;
        display: block;
      }
      #updateStatus.error {
        background: #ffebee;
        color: #b71c1c;
        border: 1px solid #e53935;
        display: block;
      }
      #updateStatus.installing {
        background: #fffde7;
        color: #f9a825;
        border: 1px solid #fbc02d;
        display: block;
      }
      #progressBarContainer {
        width: 100%;
        background: #eee;
        border-radius: 4px;
        margin-top: 16px;
        height: 24px;
        display: none;
      }
      #progressBar {
        height: 100%;
        width: 0;
        background: #1976d2;
        border-radius: 4px;
        transition: width 0.3s;
      }
      #progressStatus {
        margin-top: 8px;
        font-size: 14px;
        color: #333;
      }
      #updateConfig {
        display: none;
        margin-top: 24px;
        background: #fff; /* Kept original for update screen distinction */
        padding: 24px;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.05);
      }
      /* Section title for update screen, kept distinct */
      .section-title {
        margin-top: 24px;
        margin-bottom: 8px;
        font-size: 18px;
        color: #1976d2;
      }

      /* --- NUEVOS ESTILOS PARA MEJORAR UI DE NUEVA INSTALACIÓN --- */
      .form-section {
        background-color: rgba(0, 120, 212, 0.05); /* Subtle background */
        padding: 20px;
        border-radius: 8px;
        margin-bottom: 25px; /* Increased spacing between sections */
        border: 1px solid rgba(0, 120, 212, 0.2); /* Subtle border */
      }

      .form-section h2 {
        color: #005a9e; /* Darker, more prominent blue for section titles */
        margin-top: 0; /* Remove top margin if it's the first element */
        margin-bottom: 20px; /* Space below title */
        font-size: 18px; /* Slightly smaller than main h2, or keep at 20px */
        text-align: left;
        border-bottom: 1px solid #0078d4; /* Underline for title */
        padding-bottom: 10px; /* Space for underline */
      }
      /* --- FIN DE NUEVOS ESTILOS --- */

    </style>
  </head>
  <body>
    <!-- Pantalla de selección de tipo de instalación -->
    <div id="installTypeScreen" class="container">
      <h1>Seleccionar Tipo de Instalación</h1>
      <div class="form-group">
        <input
          type="radio"
          id="newInstall"
          name="installType"
          value="new"
          checked
        />
        <label for="newInstall">Nueva Instalación</label>
      </div>
      <div class="form-group">
        <input
          type="radio"
          id="updateInstall"
          name="installType"
          value="update"
        />
        <label for="updateInstall"
          >Actualizar Instalación Existente</label
        >
      </div>
      <button id="continueBtn">Continuar</button>
    </div>

    <!-- Pantalla de nueva instalación -->
    <div id="newInstallScreen" class="container" style="display:none">
      <h1>Nueva Instalación</h1>

      <div class="form-section"> <!-- Agrupación OpenAI -->
        <h2>Credenciales de OpenAI (Opcional si usa Gemini)</h2>
        <div class="form-group">
          <label for="openaiKey">OpenAI API Key:</label>
          <input type="text" id="openaiKey" placeholder="sk-..." />
        </div>
        <div class="form-group">
          <label for="openaiOrg">OpenAI Organization ID:</label>
          <input
            type="text"
            id="openaiOrg"
            placeholder="org-..."
          />
        </div>
      </div>

      <div class="form-section"> <!-- Agrupación Gemini -->
        <h2>Credenciales de Google Gemini (Opcional si usa OpenAI)</h2>
        <div class="form-group">
          <label for="geminiKey">Gemini API Key:</label>
          <input type="text" id="geminiKey" placeholder="Ingrese su Gemini API Key" />
        </div>
      </div>

      <div class="form-section"> <!-- Agrupación Pinecone -->
        <h2>Credenciales de Pinecone</h2>
        <div class="form-group">
          <label for="pineconeKey">Pinecone API Key:</label>
          <input type="text" id="pineconeKey" required />
        </div>
        <div class="form-group">
          <label for="pineconeIndexName">Pinecone Index Name:</label>
          <input type="text" id="pineconeIndexName" required />
        </div>
      </div>

      <div class="form-section"> <!-- Agrupación SQL -->
        <h2>Base de Datos SQL</h2>
        <div class="form-group">
          <label for="sqlUser">SQL User:</label>
          <input type="text" id="sqlUser" required />
        </div>
        <div class="form-group">
          <label for="sqlPassword">SQL Password:</label>
          <input type="password" id="sqlPassword" />
        </div>
        <div class="form-group">
          <label for="sqlServer">SQL Server:</label>
          <input type="text" id="sqlServer" required />
        </div>
        <div class="form-group">
          <label for="sqlPort">SQL Port:</label>
          <input type="number" id="sqlPort" required />
        </div>
        <div class="form-group">
          <label for="sqlDatabase">SQL Database:</label>
          <input type="text" id="sqlDatabase" required />
        </div>
      </div>

      <div class="form-section"> <!-- Agrupación Sitio -->
        <h2>Configuración del Sitio</h2>
        <div class="form-group">
          <label for="newSiteName">Nombre del sitio (será el nombre de la carpeta):</label>
          <input
            type="text"
            id="newSiteName"
            placeholder="Ingrese el nombre del sitio"
            pattern="[A-Za-z0-9]+"
            title="Solo caracteres alfanuméricos"
            required
          />
        </div>
      </div>

      <button id="installBtn">Instalar</button>
      <button id="backToInstallTypeBtnNew" class="secondary-btn">
        Atrás
      </button>
      <div id="status"></div>

      <!-- Sección para configurar el sitio (IIS) -->
      <div id="siteConfig" class="form-section" style="display:none; margin-top:25px">
        <h2>Configuración del Sitio (IIS)</h2>
        <p style="color: #666; font-size: 14px; margin-bottom: 15px;">
          La instalación se completó exitosamente. Ahora configure el sitio IIS.
        </p>
        <div class="form-group">
          <label for="sitePort">Número de puerto:</label>
          <input
            type="number"
            id="sitePort"
            placeholder="Ingrese el número de puerto"
            required
          />
        </div>
        <div class="form-group">
          <label for="siteName">Nombre del sitio:</label>
          <input
            type="text"
            id="siteName"
            placeholder="Nombre del sitio IIS"
            pattern="[A-Za-z0-9]+"
            title="Solo caracteres alfanuméricos"
            readonly
          />
        </div>
        <button id="configureIISBtn" disabled>Configurar IIS</button>
      </div>
    </div>

    <!-- Pantalla de actualización -->
    <div id="updateScreen" class="container" style="display:none">
      <h1>Actualizar Instalación</h1>
      <div class="form-group">
        <label for="apiPath">Ruta de la API:</label>
        <input
          type="text"
          id="apiPath"
          placeholder="Selecciona la carpeta de la API"
          readonly
        />
        <button id="browseBtn" class="secondary-btn" type="button">
          Explorar
        </button>
      </div>
      <div class="form-group">
        <label for="iisSiteName">Nombre del sitio IIS:</label>
        <input
          type="text"
          id="iisSiteName"
          placeholder="Ingrese el nombre del sitio IIS"
          required
        />
      </div>
      <button id="validateBtn">Validar Instalación</button>
      <div id="updateStatus"></div>
      <div id="progressBarContainer">
        <div id="progressBar"></div>
      </div>
      <div id="progressStatus"></div>
      <div id="updateConfig" style="display:none; margin-top:20px">
        <h2>Configuración Actual</h2> <!-- Este h2 usará el estilo general de h2 -->
        <div class="section-title">OpenAI (Solo lectura)</div> <!-- Esta clase es específica para updateScreen -->
        <div class="form-group">
          <label for="updateOpenaiKey">OpenAI API Key (No editable):</label>
          <input type="text" id="updateOpenaiKey" placeholder="sk-..." />
        </div>
        <div class="form-group">
          <label for="updateOpenaiOrg">OpenAI Organization ID (No editable):</label>
          <input type="text" id="updateOpenaiOrg" placeholder="org-..." />
        </div>
        <div class="section-title">Pinecone (Solo lectura)</div>
        <div class="form-group">
          <label for="updatePineconeKey">Pinecone API Key (No editable):</label>
          <input type="text" id="updatePineconeKey" />
        </div>
        <div class="section-title">Gemini (Solo lectura)</div>
        <div class="form-group">
          <label for="updateGeminiKey">Gemini API Key (No editable):</label>
          <input type="text" id="updateGeminiKey" />
        </div>
        <div class="section-title">Base de Datos SQL</div>
        <div class="form-group">
          <label for="updateSqlUser">Usuario SQL (No editable):</label>
          <input type="text" id="updateSqlUser" placeholder="SQL_USER" />
        </div>
        <div class="form-group">
          <label for="updateSqlPassword">Contraseña SQL (No editable):</label>
          <input type="password" id="updateSqlPassword" placeholder="SQL_PASSWORD" />
        </div>
        <div class="form-group">
          <label for="updateSqlServer">Servidor SQL (No editable):</label>
          <input type="text" id="updateSqlServer" placeholder="SQL_SERVER" />
        </div>
        <div class="form-group">
          <label for="updateSqlPort">Puerto SQL (No editable):</label>
          <input type="text" id="updateSqlPort" placeholder="SQL_PORT" />
        </div>
        <div class="form-group">
          <label for="updateSqlDatabase">Base de Datos (Editable):</label>
          <input type="text" id="updateSqlDatabase" placeholder="SQL_DATABASE" />
        </div>
        <button id="updateBtn">Actualizar</button>
      </div>
      <button id="backToInstallTypeBtnUpdate" class="secondary-btn">
        Atrás
      </button>
    </div>

    <!-- Pantalla de progreso -->
    <div id="progressScreen" class="container" style="display:none">
      <h1>Progreso de Instalación/Actualización</h1>
      <div
        class="progress-bar-container"
        style="width:100%; background-color:#f3f3f3; border-radius:5px; margin-top:20px"
      >
        <div
          id="progressBar"
          style="width:0%; height:25px; background-color:#4CAF50; text-align:center; line-height:25px; color:white; border-radius:5px"
          >0%</div
        >
      </div>
      <p id="progressStatus" style="text-align:center; margin-top:10px"></p>
    </div>

    <!-- Pantalla de finalización -->
    <div id="completionScreen" class="container" style="display:none">
      <h1>Proceso Completado</h1>
      <p style="text-align:center; margin-top:20px">
        La operación se ha completado con éxito.
      </p>
      <button id="finishBtn">Finalizar</button>
    </div>

    <script>
      // Navegación entre pantallas
      function showScreen(screenId) {
        document.querySelectorAll('.container').forEach((s) => {
          s.style.display = 'none';
        });
        const s = document.getElementById(screenId);
        if (s) {
          s.style.display = 'block';
          // Carga dinámica de update.js
          if (screenId === 'updateScreen' && !window.updateScriptLoaded) {
            const script = document.createElement('script');
            script.src = './update.js';
            script.async = false;
            script.onload = () => console.log('✔ update.js cargado');
            document.body.appendChild(script);
            window.updateScriptLoaded = true;
          }
        }
      }

      document.addEventListener('DOMContentLoaded', () => {
        // Inicial
        showScreen('installTypeScreen');

        // Continuar
        document
          .getElementById('continueBtn')
          .addEventListener('click', () => {
            const tipo = document.querySelector(
              'input[name="installType"]:checked'
            ).value;
            showScreen(tipo === 'new' ? 'newInstallScreen' : 'updateScreen');
          });

        // Atrás genérico
        document
          .querySelectorAll('[id^="backToInstallTypeBtn"]')
          .forEach((btn) => {
            btn.addEventListener('click', () => {
              showScreen('installTypeScreen');
            });
          });

        // Finalizar (update)
        document.getElementById('finishBtn')?.addEventListener('click', () => {
          window.close();
        });

        // ---- LÓGICA NUEVA INSTALACIÓN ----
        const installBtn = document.getElementById('installBtn');
        const backBtnNew = document.getElementById(
          'backToInstallTypeBtnNew'
        );
        const configureIISBtn = document.getElementById('configureIISBtn');
        const statusDiv = document.getElementById('status');
        const openaiKey = document.getElementById('openaiKey');
        const openaiOrg = document.getElementById('openaiOrg');
        const geminiKey = document.getElementById('geminiKey');
        const pineconeKey = document.getElementById('pineconeKey');
        const pineconeIdx = document.getElementById('pineconeIndexName');
        const sqlUser = document.getElementById('sqlUser');
        const sqlPwd = document.getElementById('sqlPassword');
        const sqlSrv = document.getElementById('sqlServer');
        const sqlPort = document.getElementById('sqlPort');
        const sqlDb = document.getElementById('sqlDatabase');
        const newSiteName = document.getElementById('newSiteName');

        installBtn.addEventListener('click', async () => {
          // validación - debe tener OpenAI O Gemini (no ambos obligatorios)
          const hasOpenAI = openaiKey.value.trim() && openaiOrg.value.trim();
          const hasGemini = geminiKey.value.trim();

          if (!hasOpenAI && !hasGemini) {
            statusDiv.className = 'error';
            statusDiv.textContent =
              'Debe proporcionar credenciales de OpenAI (API Key + Organization) O de Gemini (API Key)';
            return;
          }

          // Validar campos obligatorios restantes
          if (
            !pineconeKey.value ||
            !pineconeIdx.value ||
            !sqlUser.value ||
            !sqlSrv.value ||
            !sqlPort.value ||
            !sqlDb.value ||
            !newSiteName.value
          ) {
            statusDiv.className = 'error';
            statusDiv.textContent =
              'Por favor, complete todos los campos requeridos (Pinecone, SQL y nombre del sitio)';
            return;
          }

          // Validar formato del nombre del sitio
          if (!/^[A-Za-z0-9]+$/.test(newSiteName.value.trim())) {
            statusDiv.className = 'error';
            statusDiv.textContent =
              'El nombre del sitio solo puede tener caracteres alfanuméricos';
            return;
          }

          // bloquear UI inicial
          installBtn.disabled = true;
          backBtnNew.disabled = true;
          const inputsInit = document.querySelectorAll(
            '#newInstallScreen .form-section input' // More specific selector
          );
          inputsInit.forEach((i) => (i.disabled = true));

          statusDiv.className = 'installing';
          statusDiv.textContent = 'Instalando...';

          try {
            const siteName = newSiteName.value.trim();

            // Usar el nombre del sitio como destino
            const destination = `C:\\${siteName}`;

            const res = await window.electronAPI.install({
              destination: destination,
              openaiKey: openaiKey.value || '',
              openaiOrg: openaiOrg.value || '',
              geminiKey: geminiKey.value || '',
              pineconeKey: pineconeKey.value,
              pineconeIndexName: pineconeIdx.value,
              sqlUser: sqlUser.value,
              sqlPassword: sqlPwd.value,
              sqlServer: sqlSrv.value,
              sqlPort: sqlPort.value,
              sqlDatabase: sqlDb.value
            });
            if (res.success) {
              statusDiv.className = 'success';
              statusDiv.textContent = '¡Instalación exitosa!';
              // Mostrar la sección de configuración de IIS
              document.getElementById('siteConfig').style.display = 'block';
              // Pre-llenar el nombre del sitio en el campo de IIS (readonly)
              document.getElementById('siteName').value = siteName;
              configureIISBtn.disabled = false;
            } else {
              throw new Error(res.error);
            }
          } catch (err) {
            statusDiv.className = 'error';
            statusDiv.textContent = `Error: ${err.message}`;
            // restaurar UI en caso de fallo
            installBtn.disabled = false;
            backBtnNew.disabled = false;
            inputsInit.forEach((i) => (i.disabled = false));
          }
        });

        // ---- LÓGICA CONFIGURAR IIS ----
        configureIISBtn.addEventListener('click', async () => {
          const portIn = document.getElementById('sitePort');
          const nameIn = document.getElementById('siteName');
          const port = portIn.value.trim();
          const name = nameIn.value.trim();

          if (!port || isNaN(port)) {
            statusDiv.className = 'error';
            statusDiv.textContent = 'Ingresa un puerto válido.';
            return;
          }
          if (!name || !/^[A-Za-z0-9]+$/.test(name)) {
            statusDiv.className = 'error';
            statusDiv.textContent =
              'El nombre sólo puede tener caracteres alfanuméricos.';
            return;
          }

          // bloquear UI de IIS
          configureIISBtn.disabled = true;
          portIn.disabled = true;
          nameIn.disabled = true;
          statusDiv.className = 'installing';
          statusDiv.textContent = 'Configurando IIS...';

          try {
            const res = await window.electronAPI.configureIIS({
              destination: `C:\\${name}`,
              sitePort: port,
              siteName: name,
            });
            if (res.success) {
              statusDiv.className = 'success';
              statusDiv.textContent = 'IIS configurado exitosamente!';
              // Cambiar el botón para finalizar
              backBtnNew.textContent = 'Finalizar';
              backBtnNew.disabled = false;
              installBtn.style.display = 'none';
            } else {
              throw new Error(res.error);
            }
          } catch (err) {
            statusDiv.className = 'error';
            statusDiv.textContent = `Error: ${err.message}`;
            // restaurar UI en caso de fallo
            configureIISBtn.disabled = false;
            portIn.disabled = false;
            nameIn.disabled = false;
          }
        });

        // Configurar el botón de finalizar para cerrar la aplicación
        backBtnNew.addEventListener('click', () => {
          if (backBtnNew.textContent === 'Finalizar') {
            window.close();
          } else {
            showScreen('installTypeScreen');
          }
        });
      });
    </script>
  </body>
</html>